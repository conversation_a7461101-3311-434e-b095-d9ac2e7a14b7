<template>
  <div class="input-root">
    <!-- 以下这个 div 用于确保 vue2 的 ref 会更新 -->
    <div style="display: none">
      <div>{{ teamMute ? '禁言' : '不禁言' }}</div>
      <div>{{ isTeamMute ? '禁言' : '不禁言' }}</div>
    </div>
    <div class="msg-input-wrapper">
      <!-- 键盘上要显示的回复消息内容 -->
      <div v-if="isReplyMsg" class="reply-message-wrapper">
        <div class="reply-message-close" @tap="removeReplyMsg">
          <Icon color="#929299" :iconStyle="{ fontWeight: '200' }" :size="13" type="icon-guanbi" />
        </div>
        ｜
        <div class="reply-title">{{ t('replyText') }}</div>
        <div class="reply-noFind" v-if="replyMsg && replyMsg.idClient == 'noFind'">
          {{ t('replyNotFindText') }}
        </div>
        <div class="reply-message" v-else>
          <div class="reply-to">
            <Appellation :account="replyMsg.from" :team-id="scene === 'team' ? to : ''" color="#929299" :fontSize="13">
            </Appellation>
          </div>
          :
          <div v-if="replyMsg.type === 'text'" class="reply-msg-content">
            <message-one-line :text="replyMsg.body"></message-one-line>
          </div>
          <div v-else>{{ '[' + REPLY_MSG_TYPE_MAP[replyMsg.type] + ']' }}</div>
        </div>
      </div>

      <!-- 官方风格的固定输入框 -->
      <div class="msg-input fixed-input">
        <!-- 语音按钮 -->
        <div class="input-btn voice-btn" @tap="handleAudioVisible">
          <Icon :size="24" type="icon-audio" color="#666" />
        </div>

        <!-- 输入框区域 -->
        <div class="input-area" @tap="handleInputFocus">
          <textarea v-if="isFocus" :focus="isFocus" class="msg-input-input" :placeholder="isTeamMute ? t('teamMutePlaceholder') : null
            "v-model="inputText" type="text" :disabled="isTeamMute" :confirm-hold="true" cursor-spacing="20"
            adjust-position="true" confirm-type="send" @blur="handleInputBlur" @input="handleInput"
            @confirm="handleSendTextMsg" id="msg-input" auto-height />
          <view v-else class="input-placeholder">
            {{ isTeamMute ? t('teamMutePlaceholder') : t('chatInputPlaceHolder') }}
          </view>
        </div>

        <!-- 表情按钮 -->
        <div class="input-btn emoji-btn" @tap="handleEmojiVisible">
          <Icon :size="24" :type="emojiVisible ? 'icon-keyboard' : 'icon-biaoqing'" color="#666" />
        </div>

        <!-- 加号按钮或发送按钮 -->
        <div v-if="inputText.trim().length > 0" class="input-btn send-btn" @tap="handleSendTextMsg">
          <text>发送</text>
        </div>
        <div v-else class="input-btn plus-btn" @tap="handleSendMoreVisible">
          <Icon :size="24" type="send-more" color="#666" />
        </div>
      </div>
      <!-- 表情面板 -->
      <div v-if="emojiVisible" class="msg-emoji-panel" @click.stop="() => { }">
        <!-- 表情分类标签栏 -->
        <div class="emoji-tabs">
          <div class="emoji-tab-item" :class="{ active: emojiCategoryIndex === 0 }" @tap="emojiCategoryIndex = 0">
            <text>最近使用</text>
          </div>
          <div class="emoji-tab-item" :class="{ active: emojiCategoryIndex === 1 }" @tap="emojiCategoryIndex = 1">
            <text>所有表情</text>
          </div>
          <div class="emoji-tab-item back-to-input" @tap="switchToInput">
            <text>返回输入</text>
          </div>
        </div>

        <!-- 表情内容区 -->
        <div class="emoji-content">
          <!-- 最近使用的表情 -->
          <div class="recent-emojis" v-if="emojiCategoryIndex === 0">
            <div class="emoji-grid">
              <div class="emoji-item" v-for="(emoji, index) in recentEmojis" :key="index"
                @tap="() => handleEmoji(emoji)">
                <Icon :size="28" :type="emoji.type || 'icon-emoji-' + index" />
              </div>
            </div>
          </div>

          <!-- 所有表情 -->
          <div class="all-emojis" v-if="emojiCategoryIndex === 1">
            <Face @emojiClick="handleEmojiWithSave" @emojiDelete="handleEmojiDelete" @emojiSend="handleSendTextMsg" />
          </div>
        </div>
      </div>
      <!-- 发送语音消息面板 -->
      <div v-if="audioPanelVisible" class="msg-audio-panel" @click.stop="() => { }">
        <VoicePanel @handleSendAudioMsg="handleSendAudioMsg"></VoicePanel>
      </div>
      <!-- 发送更多面板 -->
      <div v-if="sendMoreVisible" class="send-more-panel" @click.stop="() => { }">
        <!-- 发送图片按钮 -->
        <div class="send-more-panel-item-wrapper">
          <div class="send-more-panel-item" @tap="handleSendImageMsg">
            <Icon type="icon-tupian" :size="30"></Icon>
          </div>
          <div class="icon-text">发送图片</div>
        </div>
        <!-- 拍摄按钮 -->
        <div class="send-more-panel-item-wrapper">
          <div class="send-more-panel-item" @tap="handleCameraCapture">
            <Icon type="icon-paishe" :size="30"></Icon>
          </div>
          <div class="icon-text">拍摄</div>
        </div>
        <!-- 音频通话按钮 -->
        <div class="send-more-panel-item-wrapper" v-if="isApp && scene !== 'team'">
          <div class="send-more-panel-item" @tap="handleCall(1, $event)">
            <Icon type="icon-audio-call" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('voiceCallText') }}</div>
        </div>
        <!-- 视频通话按钮 -->
        <div class="send-more-panel-item-wrapper" v-if="isApp && scene !== 'team'">
          <div class="send-more-panel-item" @tap="(event: any) => handleCall(2, event)">
            <Icon type="icon-video-call" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('videoCallText') }}</div>
        </div>
        <!-- 群视频通话按钮 -->
        <div class="send-more-panel-item-wrapper" v-if="isApp && scene == 'team'">
          <div class="send-more-panel-item" @tap="(event: any) => handleCall(2, event)">
            <Icon type="icon-video-call" :size="30"></Icon>
          </div>
          <div class="icon-text">{{ t('videoCallText') }}</div>
        </div>
      </div>
    </div>
    <!-- @消息相关 popup -->
    <UniPopup ref="popupRef" background-color="#ffffff" type="bottom" mask-background-color="rgba(0,0,0,0.4)"
      @change="onPopupChange">
      <MentionMemberList :team-id="to"></MentionMemberList>
    </UniPopup>

    <!-- 通话确认弹窗 -->
    <CallConfirmModal
      :visible="showCallConfirm"
      :title="callConfirmTitle"
      :message="callConfirmMessage"
      :confirmText="t('confirmText')"
      :cancelText="t('cancelText')"
      :callType="pendingCallType"
      @confirm="handleCallConfirm"
      @cancel="handleCallCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import type { TMsgScene } from 'nim-web-sdk-ng/dist/NIM_MINIAPP_SDK/MsgServiceInterface'
import Face from './face.vue'
import VoicePanel from './voice-panel.vue'
import Icon from '../../../components/Icon.vue'
import CallConfirmModal from '../../../components/CallConfirmModal.vue'
import {
  ref,
  getCurrentInstance,
  computed,
  onUnmounted,
  onMounted,
  defineProps,
} from '../../../utils/transformVue'
import {
  ALLOW_AT,
  events,
  REPLY_MSG_TYPE_MAP,
} from '../../../utils/constants'
import { emojiMap } from '../../../utils/emoji'
import { t } from '../../../utils/i18n'
import { handleNoPermission } from '../../../utils/permission'

import type { IMMessage } from '@xkit-yx/im-store'
import MessageOneLine from '../../../components/MessageOneLine.vue'
import {
  isAndroidApp,
  stopAllAudio,
  isIosWeb,
  isWxApp,
  startCall,
  isApp,
  startGroupCall,
} from '../../../utils'
// @ts-ignore
import UniPopup from '../../../components/uni-components/uni-popup/components/uni-popup/uni-popup.vue'
import MentionMemberList from './mention-member-list.vue'
import { autorun } from 'mobx'
import Appellation from '../../../components/Appellation.vue'
import { AT_ALL_ACCOUNT } from '../../../utils/constants'
import type { Team, TeamMember } from '@xkit-yx/im-store'
import { deepClone } from '../../../utils'

type MentionedMember = { account: string; appellation: string }
const props = defineProps({
  scene: {
    type: String,
    required: true,
  },
  to: {
    type: String,
    required: true,
  },
  replyMsgsMap: {
    type: Object,
    default: undefined,
  },
})
const inputText = ref('')
const extVisible = ref(false)
// 音频面板flag
const audioPanelVisible = ref(false)
// 发送更多面板flag
const sendMoreVisible = ref(false)
// 表情面板flag
const emojiVisible = ref(false)
// 表情分类索引，0: 最近使用, 1: 所有表情
const emojiCategoryIndex = ref(0)

// 最近使用的表情列表
const recentEmojis = ref<Array<{ key: string, type: string }>>([]);

// 通话确认弹窗相关状态
const showCallConfirm = ref(false)
const pendingCallType = ref(1) // 1: 音频, 2: 视频
const callConfirmTitle = computed(() => t('confirmCallText'))
const callConfirmMessage = computed(() =>
  pendingCallType.value === 1 ? t('confirmAudioCallText') : t('confirmVideoCallText')
)

// 初始化一些默认表情
try {
  // 尝试从本地存储加载最近使用的表情
  const savedEmojis = uni.getStorageSync('recentEmojis');
  if (savedEmojis) {
    recentEmojis.value = JSON.parse(savedEmojis);
  }
} catch (e) {
  // 如果加载失败，使用默认表情
  recentEmojis.value = [
    { key: '😀', type: 'icon-emoji-1' },
    { key: '😁', type: 'icon-emoji-2' },
    { key: '😂', type: 'icon-emoji-3' },
    { key: '😃', type: 'icon-emoji-4' },
    { key: '😄', type: 'icon-emoji-5' },
    { key: '😅', type: 'icon-emoji-6' },
    { key: '😆', type: 'icon-emoji-7' },
    { key: '😇', type: 'icon-emoji-8' },
  ];
}


// 发起呼叫，type: 1 音频呼叫，2 视频呼叫
const handleCall = (type: number, event?: Event) => {
  // 阻止默认行为和事件冒泡
  if (event && event.stopPropagation) event.stopPropagation()
  if (event && event.preventDefault) event.preventDefault()

  // 设置待处理的通话类型并显示确认弹窗
  pendingCallType.value = type
  showCallConfirm.value = true
}

// 处理通话确认
const handleCallConfirm = () => {
  showCallConfirm.value = false
  executeCall(pendingCallType.value)
}

// 处理通话取消
const handleCallCancel = () => {
  showCallConfirm.value = false
}

// 实际执行通话的方法
const executeCall = (type: number) => {
  // @ts-ignore
  const myAccount = uni.$UIKitStore.userStore.myUserInfo.account
  if (!myAccount) {
    uni.showToast({
      title: t('callFailedText'),
      icon: 'none',
    })
    return
  }
  if (props.scene === 'team') {
    // 群聊场景，调用startGroupCall
    startGroupCall({
      type: type,
      channelId: props.to,
      status: 'calling',
    })
    console.log('myAccount', myAccount, 'remoteShowName', props.to)
  } else {
    const remoteShowName = uni.$UIKitStore.uiStore.getAppellation({
      account: props.to,
    })
    startCall({
      type: type,
      name: remoteShowName,
      status: 'calling',
      userId: props.to
    })
  }
}
// 用于解决表情面板和键盘冲突，导致输入框滚动消失问题
const showEmojiInput = ref(false)

// 回复消息相关
const isReplyMsg = ref(false)
const isFocus = ref(false)
const replyMsg = ref<IMMessage>({
  idClient: '',
  from: '',
  // @ts-ignore
  type: '',
  text: '',
  body: '',
  sessionId: '',
})
// @消息相关
const ctx = getCurrentInstance()
const popupRef = ref(null)
const selectedAtMembers = ref<MentionedMember[]>([])

// 群相关
const team = ref<Team>()
const teamMembers = ref<TeamMember[]>([])
const teamMute = ref(false)

const isGroupOwner = ref(false)
const isGroupManager = ref(false)

// 是否允许@所有人
const allowAtAll = computed(() => {
  let ext: any = {}
  try {
    ext = JSON.parse((team.value || {}).ext || '{}')
  } catch (error) {
    //
  }
  if (ext[ALLOW_AT] === 'manager') {
    return isGroupOwner.value || isGroupManager.value
  }
  return true
})

// 是否是群禁言
// const isTeamMute = computed(() => {
//   console.log(
//     'isGroupOwner, isGroupManager',
//     isGroupOwner.value,
//     isGroupManager.value
//   )

//   if (!teamMute.value) {
//     return false
//   }
//   // 群主或者群管理员在群禁言时，可以发送消息
//   if (isGroupOwner.value || isGroupManager.value) {
//     return false
//   }
//   return true
// })

const isTeamMute = ref(false)

const updateTeamMute = () => {
  if (!teamMute.value) {
    isTeamMute.value = false
    return
  }
  // 群主或者群管理员在群禁言时，可以发送消息
  if (isGroupOwner.value || isGroupManager.value) {
    isTeamMute.value = false
    return
  }
  isTeamMute.value = true
  return
}

const onPopupChange = (e: any) => {
  uni.$emit(events.HANDLE_MOVE_THROUGH, e.value)
}

// 点击@群成员
const handleMentionItemClick = (member: MentionedMember) => {
  // @ts-ignore
  ctx.refs.popupRef.close()
  uni.$emit(events.HANDLE_MOVE_THROUGH, false)
  const nickInTeam = member.appellation
  selectedAtMembers.value = [
    ...selectedAtMembers.value.filter(
      (item) => item.account !== member.account
    ),
    member,
  ]
  const newInputText = inputText.value + nickInTeam + ' '
  // 更新input框的内容
  inputText.value = newInputText
}

const closePopup = () => {
  // @ts-ignore
  ctx.refs.popupRef.close()
}



const handleInputFocus = () => {
  // 不自动关闭表情面板，允许用户同时输入文字和选择表情
  // emojiVisible.value = false
  isFocus.value = true
  audioPanelVisible.value = false
  sendMoreVisible.value = false

  if (isAndroidApp) {
    setTimeout(() => {
      isFocus.value = true
    }, 300)
  }
  if (isIosWeb) {
    const inputRoot = document.getElementById('msg-input')
    setTimeout(() => {
      inputRoot?.scrollIntoView()
    }, 300)
  }

  // 滚动到底部
  uni.$emit(events.ON_SCROLL_BOTTOM)
}

const handleInputBlur = () => {
  // 如果表情面板或其他面板打开，不设置失焦状态
  if (!emojiVisible.value && !audioPanelVisible.value && !sendMoreVisible.value) {
    isFocus.value = false
  }
}

// 滚动到底部
const scrollBottom = () => {
  if (isAndroidApp || isWxApp) {
    setTimeout(() => {
      uni.$emit(events.ON_SCROLL_BOTTOM)
    }, 300)
  } else {
    uni.$emit(events.ON_SCROLL_BOTTOM)
  }
}

// 输入框输入事件
const handleInput = (event: any) => {
  const text = event?.detail?.value
  const isAit = text.endsWith('@') || text.endsWith('@\n')
  if (props.scene == 'team') {
    if (isAit) {
      // 当前输入的是@
      uni.hideKeyboard()
      // @ts-ignore
      ctx.refs.popupRef.open('bottom')
      isFocus.value = false
      uni.$emit(events.HANDLE_MOVE_THROUGH, true)
    }
  }
}

// 发送文本消息
const handleSendTextMsg = async () => {
  if (inputText.value.trim() === '') return
  const ext = onAtMembersExtHandler()

  // 关闭所有面板
  emojiVisible.value = false
  extVisible.value = false
  audioPanelVisible.value = false
  sendMoreVisible.value = false

  // const dataInfo = {
  // 	scene: props.scene as TMsgScene,
  // 	to: props.to,
  // 	body: inputText.value,
  // 	ext: selectedAtMembers.value.length && ext,
  // };
  // const team = uni.$UIKitStore.teamStore.teams.get(props.to)
  // console.log(team)
  // 一对一聊天
  // const userInfo = uni.$UIKitStore.userStore.myUserInfo;
  // if(dataInfo.scene == 'p2p'){
  // 	if(!toUserInfo){
  // 		toUserInfo = await uni.$UIKitStore.userStore.getUserForceActive(props.to)
  // 	}
  // 	const dbServerData = {
  // 		scene:dataInfo.scene,
  // 		to:props.to,
  // 		toName:toUserInfo?.nick || '未知',
  // 		toAvatar:toUserInfo?.avatar || '',
  // 		msgType:'txt',
  // 		content:inputText.value,
  // 		fromAccount:userInfo.account,
  // 		fromNick:userInfo.nick,
  // 		fromAvatar:userInfo.avatar,
  // 	}
  // 	console.log(dbServerData);
  // }else if(dataInfo.scene == 'team' || dataInfo.scene == 'superTeam'){
  // 	if(!toTeamInfo){
  // 		toTeamInfo = uni.$UIKitStore.teamStore.teams.get(props.to)
  // 	}
  // 	const dbServerData = {
  // 		scene:dataInfo.scene,
  // 		to:props.to,
  // 		toName:toTeamInfo?.name || '未知',
  // 		toAvatar:toTeamInfo?.avatar || '',
  // 		msgType:'txt',
  // 		content:inputText.value,
  // 		fromAccount:userInfo.account,
  // 		fromNick:userInfo.nick,
  // 		fromAvatar:userInfo.avatar,
  // 	}
  // 	console.log(dbServerData)
  // }
  // @ts-ignore
  uni.$UIKitStore.msgStore
    .sendTextMsgActive({
      scene: props.scene as TMsgScene,
      to: props.to,
      body: inputText.value,
      ext: selectedAtMembers.value.length && ext,
    })
    .finally(() => {
      if (isAndroidApp) {
        setTimeout(() => {
          uni.$emit(events.ON_SCROLL_BOTTOM)
        }, 300)
      } else {
        uni.$emit(events.ON_SCROLL_BOTTOM)
      }
    })
  inputText.value = ''
  isReplyMsg.value = false
  selectedAtMembers.value = []
}

// 移除回复消息
const removeReplyMsg = () => {
  // @ts-ignore
  uni.$UIKitStore.msgStore.removeReplyMsgActive(
    replyMsg?.value?.sessionId as string
  )
  isReplyMsg.value = false
}

// 显示表情面板
const handleEmojiVisible = () => {
  if (isTeamMute.value) return

  // 切换表情面板的显示状态
  emojiVisible.value = !emojiVisible.value

  if (emojiVisible.value) {
    // 打开表情面板
    extVisible.value = true
    showEmojiInput.value = true
    audioPanelVisible.value = false
    sendMoreVisible.value = false

    // 隐藏键盘
    uni.hideKeyboard()
    // 关闭输入框聚焦
    isFocus.value = false
  } else {
    // 关闭表情面板
    extVisible.value = false
    showEmojiInput.value = false

    // 聚焦输入框
    setTimeout(() => {
      isFocus.value = true
    }, 100)
  }

  // 滚动到底部
  uni.$emit(events.ON_SCROLL_BOTTOM)
}

// 切换到输入框
const switchToInput = () => {
  // 关闭表情面板
  emojiVisible.value = false
  extVisible.value = false
  showEmojiInput.value = false

  // 聚焦输入框
  setTimeout(() => {
    isFocus.value = true
  }, 100)

  // 滚动到底部
  uni.$emit(events.ON_SCROLL_BOTTOM)
}



// 显示发送更多"+"面板
const handleSendMoreVisible = () => {
  if (isTeamMute.value) return
  audioPanelVisible.value = false
  emojiVisible.value = false
  sendMoreVisible.value = !sendMoreVisible.value
  setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM)
  }, 300)
}

// 点击表情
const handleEmoji = (emoji: { key: string; type: string }) => {
  // 添加表情到输入框
  inputText.value += emoji.key

  // 聚焦输入框
  isFocus.value = true

  // 如果是直接点击发送表情，可以自动发送
  if (emoji.key && emoji.key.length > 0 && !inputText.value.includes('\n')) {
    // 可以选择自动发送或者等待用户手动发送
    // handleSendTextMsg()
  }
}

// 点击表情并保存到最近使用
const handleEmojiWithSave = (emoji: { key: string; type: string }) => {
  // 先调用原来的处理函数
  handleEmoji(emoji)

  // 将表情添加到最近使用列表
  const existingIndex = recentEmojis.value.findIndex(item => item.key === emoji.key)

  if (existingIndex !== -1) {
    // 如果已存在，删除旧的
    recentEmojis.value.splice(existingIndex, 1)
  }

  // 将新表情添加到列表开头
  recentEmojis.value.unshift(emoji)

  // 保持最近使用列表不超过8个
  if (recentEmojis.value.length > 8) {
    recentEmojis.value = recentEmojis.value.slice(0, 8)
  }

  // 保存到本地存储
  try {
    uni.setStorageSync('recentEmojis', JSON.stringify(recentEmojis.value))
  } catch (e) {
    console.error('Failed to save recent emojis', e)
  }
}

// 删除表情
const handleEmojiDelete = () => {
  let target = ''
  const isEmojiEnd = Object.keys(emojiMap).reduce((prev, cur) => {
    const isEnd = inputText.value.endsWith(cur)
    if (isEnd) {
      target = cur
    }
    return prev || isEnd
  }, false)
  if (isEmojiEnd && target) {
    inputText.value = inputText.value.replace(target, '')
  } else {
    inputText.value = inputText.value.slice(0, -1)
  }
}

// 显示语音面板
const handleAudioVisible = () => {
  if (isTeamMute.value) return
  audioPanelVisible.value = !audioPanelVisible.value
  emojiVisible.value = false
  setTimeout(() => {
    uni.$emit(events.ON_SCROLL_BOTTOM)
  }, 300)
}

// 发送图片和视频消息
const handleSendImageMsg = () => {
  if (isTeamMute.value) return
  stopAllAudio()

  // 关闭所有面板
  emojiVisible.value = false
  extVisible.value = false
  audioPanelVisible.value = false
  sendMoreVisible.value = false

  // 直接从相册选择图片和视频
  uni.chooseMedia({
    count: 9,
    mediaType: ['image', 'video'],
    sourceType: ['album'], // 只从相册选择，不显示拍摄选项
    maxDuration: 60,
    sizeType: ['compressed'],
    success: (res) => {
      // 处理所有选择的媒体文件
      const sendPromises = res.tempFiles.map((file: any) => {
        const filePath = file.tempFilePath
        const fileType = file.fileType || (file.tempFilePath.match(/\.(mp4|mov|3gp|avi)$/i) ? 'video' : 'image')

        if (fileType === 'video') {
          // 发送视频
          // @ts-ignore
          return uni.$UIKitStore.msgStore
            .sendVideoMsgActive({
              scene: props.scene as TMsgScene,
              to: props.to,
              filePath: filePath,
              onUploadStart: () => {
                scrollBottom()
              },
            })
            .catch((error) => {
              console.error('发送视频失败:', error)
              uni.showToast({
                icon: 'error',
                title: t('sendVideoFailedText'),
              })
              // 返回一个已解决的Promise，以便Promise.all不会因为单个视频发送失败而中断
              return Promise.resolve()
            })
        } else {
          // 发送图片
          // @ts-ignore
          return uni.$UIKitStore.msgStore
            .sendImageMsgActive({
              scene: props.scene as TMsgScene,
              to: props.to,
              filePath: filePath,
            })
            .catch((error) => {
              console.error('发送图片失败:', error)
              uni.showToast({
                icon: 'error',
                title: t('sendImageFailedText'),
              })
              // 返回一个已解决的Promise，以便Promise.all不会因为单个图片发送失败而中断
              return Promise.resolve()
            })
        }
      })

      // 等待所有媒体文件发送完成
      Promise.all(sendPromises)
        .then(() => {
          scrollBottom()
          if (res.tempFiles.length > 1) {
            uni.showToast({
              icon: 'success',
              title: `已发送${res.tempFiles.length}个文件`,
            })
          }
        })
        .catch(() => {
          scrollBottom()
        })
    },
    //没有开启权限时，提示开启权限
    complete: handleNoPermission,
  })
}



// 处理拍摄按钮点击，打开相机进行拍照或录像
const handleCameraCapture = () => {
  if (isTeamMute.value) return
  stopAllAudio()

  // 关闭所有面板
  emojiVisible.value = false
  extVisible.value = false
  audioPanelVisible.value = false
  sendMoreVisible.value = false

  // 显示操作菜单，让用户选择拍照或录像
  uni.showActionSheet({
    itemList: [t('takePhotoText') || '拍照', t('recordVideoText') || '录像'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 拍照
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          sizeType: ['compressed'],
          success: (res) => {
            // 发送拍摄的照片
            const filePath = res.tempFilePaths[0]
            // @ts-ignore
            uni.$UIKitStore.msgStore
              .sendImageMsgActive({
                scene: props.scene as TMsgScene,
                to: props.to,
                filePath: filePath,
              })
              .catch((error) => {
                console.error('发送图片失败:', error)
                uni.showToast({
                  icon: 'error',
                  title: t('sendImageFailedText'),
                })
              })
              .finally(() => {
                scrollBottom()
              })
          },
          complete: handleNoPermission,
        })
      } else if (res.tapIndex === 1) {
        // 录像
        uni.chooseVideo({
          sourceType: ['camera'],
          compressed: true,
          maxDuration: 60,
          success: (res) => {
            // @ts-ignore
            uni.$UIKitStore.msgStore
              .sendVideoMsgActive({
                scene: props.scene as TMsgScene,
                to: props.to,
                filePath: res.tempFilePath,
                onUploadStart: () => {
                  scrollBottom()
                },
              })
              .then(() => {
                scrollBottom()
              })
              .catch(() => {
                scrollBottom()
                uni.showToast({
                  icon: 'error',
                  title: t('sendVideoFailedText'),
                })
              })
          },
          complete: handleNoPermission,
        })
      }
    }
  })
}

// 发送语音消息
const handleSendAudioMsg = (filePath: string, duration: number) => {
  // 关闭所有面板
  emojiVisible.value = false
  extVisible.value = false
  audioPanelVisible.value = false
  sendMoreVisible.value = false

  // @ts-ignore
  uni.$UIKitStore.msgStore
    .sendAudioMsgActive({
      scene: props.scene,
      to: props.to,
      filePath,
      duration,
      onUploadStart: () => {
        scrollBottom()
      },
    })
    .catch(() => {
      uni.showToast({
        icon: 'error',
        title: t('sendAudioFailedText'),
      })
      scrollBottom()
    })
}



let uninstallTeamWatch = () => { }

onMounted(() => {
  uninstallTeamWatch = autorun(() => {
    if (props.scene === 'team') {
      // @ts-ignore
      const team = deepClone(uni.$UIKitStore.teamStore.teams.get(props.to))
      teamMembers.value = deepClone(
        // @ts-ignore
        uni.$UIKitStore.teamMemberStore.getTeamMember(props.to)
      )
      // @ts-ignore
      const myUser = uni.$UIKitStore.userStore.myUserInfo
      isGroupOwner.value = team?.owner == myUser.account
      isGroupManager.value = teamMembers.value
        .filter((item) => item.type === 'manager')
        .some((member) => member.account === (myUser ? myUser.account : ''))
      team.value = team
      teamMute.value = team.mute
      updateTeamMute()
    }
  })
  // 撤回后，重新编辑消息
  uni.$on(events.ON_REEDIT_MSG, (msg: IMMessage) => {
    const _replyMsg = props.replyMsgsMap?.[msg.idClient]
    // 如果重新编辑的是回复消息，则需要将回复消息展示在输入框上方
    if (_replyMsg?.idClient) {
      // @ts-ignore
      _replyMsg && uni.$UIKitStore.msgStore.replyMsgActive(_replyMsg)
      replyMsg.value = _replyMsg
      isReplyMsg.value = true
    }
    // 如果重新编辑的是@消息，则需要将被@的成员重新加入selectedAtMembers
    if (msg.ext) {
      const extObj = JSON.parse(msg.ext)
      const yxAitMsg = extObj.yxAitMsg
      if (yxAitMsg) {
        const _mentionedMembers: MentionedMember[] = []
        Object.keys(yxAitMsg).forEach((key) => {
          if (key == AT_ALL_ACCOUNT) {
            _mentionedMembers.push({
              account: key,
              appellation: '所有人',
            })
          } else {
            _mentionedMembers.push({
              account: key,
              // @ts-ignore
              appellation: uni.$UIKitStore.uiStore.getAppellation({
                account: key,
                teamId: props.to,
                ignoreAlias: true,
              }),
            })
          }
        })
        selectedAtMembers.value = _mentionedMembers
      }
    }
    inputText.value = msg?.attach?.oldBody
    isFocus.value = true
  })

  uni.$on(events.REPLY_MSG, (msg: IMMessage) => {
    isReplyMsg.value = true
    isFocus.value = true
    replyMsg.value = msg
  })

  uni.$on(events.AIT_TEAM_MEMBER, (member) => {
    selectedAtMembers.value = [
      ...selectedAtMembers.value.filter(
        (item) => item.account !== member.account
      ),
      member,
    ]
    const newInputText = inputText.value + '@' + member.appellation + ' '
    // 更新input框的内容
    inputText.value = newInputText
  })

  // 关闭表情、语音、发送更多面板
  uni.$on(events.CLOSE_PANEL, () => {
    emojiVisible.value = false
    extVisible.value = false
    audioPanelVisible.value = false
    sendMoreVisible.value = false
  })

  // @消息 @群成员
  uni.$on(events.HANDLE_AIT_MEMBER, (member) => {
    handleMentionItemClick(member)
  })

  // 关闭@群成员面板
  uni.$on(events.CLOSE_AIT_POPUP, () => {
    closePopup()
  })

  // 表情点击
  uni.$on(events.EMOJI_CLICK, (emoji) => {
    handleEmoji(emoji)
  })

  // 表情删除
  uni.$on(events.EMOJI_DELETE, () => {
    handleEmojiDelete()
  })

  // 表情发送
  uni.$on(events.EMOJI_SEND, () => {
    emojiVisible.value = false
    extVisible.value = false
    handleSendTextMsg()
  })

  if (uni.onKeyboardHeightChange) {
    uni.onKeyboardHeightChange((res) => {
      const isAndroidWxapp =
        uni.getSystemInfoSync().platform == 'android' && isWxApp
      // 此处是为了点击安卓键盘上的收起按钮时，表情面板需要隐藏
      if (
        (res.height === 0 && isAndroidApp) ||
        (res.height === 0 && isAndroidWxapp)
      ) {
        emojiVisible.value = false
        extVisible.value = false
      }
    })
  }
})

const onAtMembersExtHandler = () => {
  let ext: any
  if (selectedAtMembers.value.length) {
    selectedAtMembers.value
      .filter((member) => {
        if (!allowAtAll.value && member.account === AT_ALL_ACCOUNT) {
          return false
        }
        return true
      })
      .forEach((member) => {
        // @ts-ignore
        const substr = `@${member.appellation}`
        const positions: number[] = []
        let pos = inputText.value?.indexOf(substr)
        while (pos !== -1) {
          positions.push(pos)
          pos = inputText.value?.indexOf(substr, pos + 1)
        }
        if (positions.length) {
          if (!ext) {
            ext = {
              yxAitMsg: {
                [member.account]: {
                  text: substr,
                  segments: [],
                },
              },
            }
          } else {
            ext.yxAitMsg[member.account] = {
              text: substr,
              segments: [],
            }
          }
          positions.forEach((position) => {
            const start = position
            ext.yxAitMsg[member.account].segments.push({
              start,
              end: start + substr.length,
              broken: false,
            })
          })
        }
      })
  }
  return ext
}

onUnmounted(() => {
  uni.$off(events.REPLY_MSG)
  uni.$off(events.ON_REEDIT_MSG)
  uni.$off(events.REPLY_MSG)
  uni.$off(events.AIT_TEAM_MEMBER)
  // 关闭表情面板
  uni.$off(events.CLOSE_PANEL)
  // @消息 @群成员
  uni.$off(events.HANDLE_AIT_MEMBER)
  // 关闭@群成员面板
  uni.$off(events.CLOSE_AIT_POPUP)
  // 表情点击
  uni.$off(events.EMOJI_CLICK)
  // 表情删除
  uni.$off(events.EMOJI_DELETE)
  // 表情发送
  uni.$off(events.EMOJI_SEND)
  removeReplyMsg()
  uninstallTeamWatch()
})
</script>

<style scoped lang="scss">
@import '../../styles/common.scss';

.input-box {
  display: flex;
  width: 100%;
  align-items: center;

  .send-btn {
    width: 90rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2A6BF2;
    color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    margin-left: 20rpx;
    box-shadow: 0 2px 4px rgba(42, 107, 242, 0.2);
  }
}

.input-root {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 300px;
}

.input-root-h5 {
  height: auto;
  position: relative;
  order: 1;
}

.msg-input-wrapper {
  width: 100%;
  height: 100%;
  background-color: #eff1f3;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  z-index: 999;
}

.msg-input {
  overflow-x: hidden;
  padding: 10px;
  background-color: #eff1f3;
  border-top: 1px solid #e0e0e0;

  &-input {
    background-color: #fff;
    height: 40px;
    font-size: 16px;
    padding: 0 12px;
    border-radius: 8px;
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e8e8e8;
    flex: 1;

    &::placeholder {
      padding: 0 12px;
      color: #999;
    }
  }
}



.msg-ext {
  overflow-y: auto;
  width: 100%;
  height: 300px;
  background-color: #eff1f3;
  z-index: 1;
}

.msg-emoji-panel {
  overflow-y: auto;
  width: 100%;
  height: 220px;
  background-color: #f9f9f9;
  z-index: 9;
  border-top: 1px solid #e5e5e5;
  position: fixed;
  bottom: 52px;
  /* 输入框高度 + 内边距 */
  left: 0;
  right: 0;

  .emoji-tabs {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e5e5e5;
    position: sticky;
    top: 0;
    z-index: 2;

    .emoji-tab-item {
      flex: 1;
      padding: 10px 0;
      font-size: 14px;
      color: #666;
      text-align: center;
      position: relative;

      &.active {
        color: #0089ff;

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 30%;
          right: 30%;
          height: 2px;
          background-color: #0089ff;
        }
      }

      &.back-to-input {
        color: #0089ff;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .emoji-content {
    padding: 10px 0;
  }

  .emoji-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 0 10px;

    .emoji-item {
      width: 12.5%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }
    }
  }

  .all-emojis {
    padding-bottom: 10px;
  }
}

.fixed-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  // align-items:end;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-top: 1px solid #e5e5e5;

  .input-btn {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 6px;

    &:active {
      opacity: 0.7;
    }
  }

  .input-area {
    flex: 1;
    background-color: #fff;
    min-height: 36px;
    border-radius: 18px;
    margin: 0 8px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border: 1px solid #e5e5e5;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #0089ff;
      box-shadow: 0 0 0 2px rgba(0, 137, 255, 0.1);
    }

    .msg-input-input {
      width: 100%;
      height: 36px;
      font-size: 15px;
      border: none;
      outline: none;
      background: transparent;
      color: #333;
      padding: 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      max-height: 100px;
      overflow: hidden;
    }

    .input-placeholder {
      color: #999;
      font-size: 15px;
      line-height: 36px;
      height: 36px;
      display: flex;
      align-items: center;
    }
  }

  .send-btn {
    background-color: #0089ff;
    color: #fff;
    border-radius: 16px;
    width: auto;
    padding: 0 12px;
    font-size: 14px;
    height: 32px;

    &:active {
      background-color: #0077e6;
    }
  }

  .input-btn, .send-btn {
    align-self: flex-end; /* 按钮固定在底部 */
    margin-bottom: 2px; /* 微调底部间距 */
  }
}

.msg-audio-panel {
  overflow-y: hidden;
  width: 100%;
  height: 300px;
  background-color: #eff1f3;
  z-index: 1;
}

.send-more-panel {
  display: flex;
  padding: 15px;
  overflow-y: hidden;
  width: 100%;
  height: 300px;
  background-color: #eff1f3;
  z-index: 1;
}

.reply-message-wrapper {
  display: flex;
  font-size: 13px;
  background-color: #eff1f2;
  height: 25px;
  padding-top: 6px;
  align-items: center;
  color: #929299;

  .reply-noFind {
    width: fit-content;
  }

  .reply-message-close {
    flex-basis: 14px;
    margin-left: 10px;
    display: flex;
    align-items: center;
  }

  .reply-message {
    display: flex;
    align-items: center;
    flex-basis: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .reply-title {
    flex-basis: 30px;
    white-space: nowrap;
    margin-right: 5px;
  }

  .reply-to {
    flex-basis: content;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
  }
}

.input-emoji {
  background-color: #fff;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  padding: 0 12px;
  border-radius: 6px;
}

.input-text {
  white-space: nowrap;
}

.input-placeholder {
  background-color: #fff;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  padding: 0 12px;
  border-radius: 6px;
  color: gray;
}

.send-more-panel-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  .send-more-panel-item {
    background-color: #fff;
    border-radius: 8px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    margin: 0 12px;
    justify-content: center;
  }

  .icon-text {
    font-size: 12px;
    color: #747475;
    margin-top: 8px;
  }
}

.msg-input-input {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
</style>
